import React, { useState, useEffect, useRef, useCallback } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeHighlight from "rehype-highlight";
import {
  AIFilled,
  ArrowDowmOutlined,
  <PERSON>U<PERSON>Outlined,
  CopyIcon,
  RefreshIcon,
  BeFondOfOutlined,
  BeFondOfFilled,
  BeNotFondOfOutlined,
  BeNotFondOfFilled,
} from "@/components/icon";
import { Tooltip, message as antdMessage } from "antd";
import { FormattedMessage } from "umi";
import { isCN } from "@/utils/index";
import config from "@/services/hostConfig";
import styles from "./index.module.less";

const TypingIndicator = () => (
  <div className="typingIndicator">
    <div className="typingDot" style={{ "--delay": "0ms" }}></div>
    <div className="typingDot" style={{ "--delay": "150ms" }}></div>
    <div className="typingDot" style={{ "--delay": "300ms" }}></div>
  </div>
);

// 获取当前环境
const env = process.env.UMI_APP_ENV || "prod";

const ChatMessage = ({
  message,
  onCopy,
  onLike,
  onDislike,
  onRegenerate,
  showCustomButton,
  onCustomAction,
  isGenerating = false,
}) => {
  const isUser = message.role === "user";
  const [showThoughts, setShowThoughts] = useState(false);
  const contentRef = useRef(null);
  const lastContentRef = useRef("");

  // 检查是否有思考过程内容
  const hasThoughts =
    !isUser &&
    message.thoughts &&
    typeof message.thoughts === "string" &&
    message.thoughts.trim().length > 0;

  // 处理用户消息中的企业名称，将其转换为可点击的链接
  const processEntityLinksForDisplay = useCallback((text, entities) => {
    if (!text || !entities || entities.length === 0) {
      return text;
    }

    let processedText = text;

    // 将企业名称包装为可点击的链接
    entities.forEach((entity) => {
      if (entity.name && entity.id && entity.country) {
        const linkUrl = `https://bizr.${config[env].location}/enterprise#/detail?tid=${entity.id}&name=${encodeURIComponent(entity.name)}&country=${entity.country}`;
        const linkHtml = ` <a href="${linkUrl}" target="_blank" rel="noopener noreferrer">${entity.name}</a> `;
        const escapedName = entity.name.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
        const regex = new RegExp(escapedName, "g");
        processedText = processedText.replace(regex, linkHtml.trim());
      }
    });

    return processedText;
  }, []);

  // 移除单个消息的滚动逻辑，由ChatArea统一处理滚动
  useEffect(() => {
    lastContentRef.current = message.content;
  }, [message.content]);

  const handleCopy = () => {
    if (onCopy) {
      onCopy(message.content);
      // 显示复制成功提示
      antdMessage.success(isCN ? "复制成功" : "Copied");
    }
  };

  return (
    <div
      className={`${styles.messageContainer} ${isUser ? styles.userMessage : styles.assistantMessage}`}
      style={
        isUser
          ? { justifyContent: "flex-end" }
          : { justifyContent: "flex-start" }
      }
    >
      {/* 只为AI助手显示头像 */}
      {!isUser && <AIFilled />}

      {/* 消息内容 */}
      <div
        className={`${styles.messageContent} ${isUser ? styles.userContent : styles.assistantContent}`}
        style={
          isUser ? { marginLeft: "auto", marginRight: 0, maxWidth: "70%" } : {}
        }
      >
        {/* 思考过程 */}
        {!isUser && hasThoughts && (
          <div
            className={`${styles.thoughtsSection} ${message.isThinking ? "thinking" : ""}`}
          >
            <div
              onClick={() => setShowThoughts(!showThoughts)}
              className={styles.thoughtsToggle}
            >
              <span>
                {message.content ? (
                  <FormattedMessage id="ai-think-process" />
                ) : (
                  <FormattedMessage id="ai-thinking" />
                )}
              </span>
              {showThoughts ? <ArrowUpOutlined /> : <ArrowDowmOutlined />}
            </div>
            {showThoughts && (
              <div className={styles.thoughtsContent}>
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  rehypePlugins={[rehypeHighlight]}
                >
                  {message.thoughts}
                </ReactMarkdown>
                {message.isThinking && <TypingIndicator />}
              </div>
            )}
          </div>
        )}

        {/* 回复内容 */}
        {(!message.isThinking || message.content) && (
          <div
            className={`${styles.messageBubble} ${isUser ? styles.user : styles.assistant}`}
          >
            {message.isTyping && !message.content ? (
              <TypingIndicator />
            ) : (
              <div className={styles.messageUserText} ref={contentRef}>
                {isUser ? (
                  <div
                    className={styles.messageText}
                    dangerouslySetInnerHTML={{
                      __html: processEntityLinksForDisplay(
                        message.content,
                        message.entities || []
                      )
                        ?.split("\n")
                        ?.map(
                          (line) =>
                            `<p style="word-wrap: break-word; overflow-wrap: break-word; word-break: break-word; white-space: pre-wrap;">${line}</p>`
                        )
                        ?.join(""),
                    }}
                  />
                ) : (
                  <div className={styles.messageText}>
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      rehypePlugins={[rehypeHighlight]}
                      components={{
                        // 为所有文本元素添加强制换行样式
                        p: ({ children }) => (
                          <p
                            style={{
                              margin: 0,
                              padding: 0,
                              wordWrap: "break-word",
                              overflowWrap: "break-word",
                              wordBreak: "break-word",
                              whiteSpace: "pre-wrap",
                              lineHeight: "24px",
                            }}
                          >
                            {children}
                          </p>
                        ),
                        div: ({ children }) => (
                          <div
                            style={{
                              margin: 0,
                              padding: 0,
                              wordWrap: "break-word",
                              overflowWrap: "break-word",
                              wordBreak: "break-word",
                            }}
                          >
                            {children}
                          </div>
                        ),
                        span: ({ children }) => (
                          <span
                            style={{
                              margin: 0,
                              padding: 0,
                              wordWrap: "break-word",
                              overflowWrap: "break-word",
                              wordBreak: "break-word",
                            }}
                          >
                            {children}
                          </span>
                        ),
                        ul: ({ children }) => (
                          <ul
                            style={{
                              margin: 0,
                              padding: 0,
                              paddingLeft: "1.5em",
                              lineHeight: "1.6",
                              wordWrap: "break-word",
                              overflowWrap: "break-word",
                            }}
                          >
                            {children}
                          </ul>
                        ),
                        ol: ({ children }) => (
                          <ol
                            style={{
                              margin: 0,
                              padding: 0,
                              paddingLeft: "1.5em",
                              lineHeight: "1.6",
                              wordWrap: "break-word",
                              overflowWrap: "break-word",
                            }}
                          >
                            {children}
                          </ol>
                        ),
                        li: ({ children }) => (
                          <li
                            style={{
                              margin: 0,
                              padding: 0,
                              lineHeight: "1.6",
                              wordWrap: "break-word",
                              overflowWrap: "break-word",
                              wordBreak: "break-word",
                            }}
                          >
                            {children}
                          </li>
                        ),
                        h1: ({ children }) => (
                          <h1
                            style={{
                              fontSize: "1.5em",
                              margin: 0,
                              padding: 0,
                              fontWeight: "bold",
                              lineHeight: "1.4",
                              wordWrap: "break-word",
                              overflowWrap: "break-word",
                              wordBreak: "break-word",
                            }}
                          >
                            {children}
                          </h1>
                        ),
                        h2: ({ children }) => (
                          <h2
                            style={{
                              fontSize: "1.3em",
                              margin: 0,
                              padding: 0,
                              fontWeight: "bold",
                              lineHeight: "1.4",
                              wordWrap: "break-word",
                              overflowWrap: "break-word",
                              wordBreak: "break-word",
                            }}
                          >
                            {children}
                          </h2>
                        ),
                        h3: ({ children }) => (
                          <h3
                            style={{
                              fontSize: "1.1em",
                              margin: 0,
                              padding: 0,
                              fontWeight: "bold",
                              lineHeight: "1.4",
                              wordWrap: "break-word",
                              overflowWrap: "break-word",
                              wordBreak: "break-word",
                            }}
                          >
                            {children}
                          </h3>
                        ),
                      }}
                    >
                      {message.content}
                    </ReactMarkdown>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* 消息操作按钮 */}
        {!isUser && !message.isTyping && message.content && (
          <div className={styles.messageActions}>
            <div onClick={handleCopy} className={styles.actionButton}>
              <Tooltip title={<FormattedMessage id="message-copy" />}>
                <CopyIcon />
              </Tooltip>
            </div> 
            {/* 当前会话有AI正在输出时，隐藏重新生成按钮 */}
            {!isGenerating && (
              <div
                onClick={() => onRegenerate?.(message.id)}
                className={styles.actionButton}
              >
                <Tooltip title={<FormattedMessage id="message-regenerate" />}>
                  <RefreshIcon />
                </Tooltip>
              </div>
            )}
            <div className={styles.line} />
            <div
              onClick={() => onDislike?.(message.id)}
              className={styles.actionButton}
            >
              {message.like === false ? (
                <BeNotFondOfFilled />
              ) : (
                <BeNotFondOfOutlined />
              )}
            </div>
            <div
              onClick={() => onLike?.(message.id)}
              className={styles.actionButton}
            >
              {message.like === true ? (
                <BeFondOfFilled />
              ) : (
                <BeFondOfOutlined />
              )}
            </div>
          </div>
        )}

        {/* 定制化按钮 */}
        {showCustomButton &&
          message.tips &&
          Array.isArray(message.tips) &&
          message.tips.length > 0 && (
            <div className={styles.customActionContainer}>
              {message.tips.map((tip, index) => (
                <span
                  key={index}
                  className={styles.customActionButton}
                  onClick={() => {
                    onCustomAction?.(tip);
                  }}
                >
                  {tip}
                </span>
              ))}
            </div>
          )}
      </div>
    </div>
  );
};

export default ChatMessage;
